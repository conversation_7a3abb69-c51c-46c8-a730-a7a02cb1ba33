import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import countriesWithPhoneNumberLength from "../../../../data/countriesWithPhoneNumberLength.json";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { toast } from "react-toastify";
import { register } from "../../../../services/operations/authAPI";

const SignUpForm = () => {
  const navigate = useNavigate();
  const [userData, setUserData] = useState({
    name: "",
    email: "",
    phone: "",
    countryCode: "",
    linkedinUrl: "",
    userType: "recruiter",
    isEligible: "false",
    password: "",
  });

  const [showPassword, setShowPassword] = useState(false);

  const [termsAccepted, setTermsAccepted] = useState(false);
  const [loading, setLoading] = useState(false);

  const userDataHandler = (e) => {
    setUserData({ ...userData, [e.target.name]: e.target.value });
  };

  const mobileInputHandler = (value, country) => {
    setUserData({
      ...userData,
      phone: value, // full number with country code
      countryCode: country.dialCode,
    });
  };

  const isFormValid = () => {
    return (
      userData.name.trim() !== "" &&
      userData.email.trim() !== "" &&
      userData.phone.trim() !== "" &&
      userData.countryCode.trim() !== "" &&
      userData.linkedinUrl.trim() !== "" &&
      userData.isEligible !== "" &&
      termsAccepted &&
      userData.password.trim() !== ""
    );
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!userData.name.trim()) {
      toast.error("Name is required");
      return;
    }
    if (!userData.email.trim()) {
      toast.error("Email is required");
      return;
    }
    if (!userData.password.trim()) {
      toast.error("Password is required");
      return;
    }
    if (!userData.phone.trim()) {
      toast.error("Phone number is required");
      return;
    }
    if (!userData.linkedinUrl.trim()) {
      toast.error("LinkedIn URL is required");
      return;
    }
    if (!termsAccepted) {
      toast.error("You must accept the Terms & Conditions");
      return;
    }

    try {
      setLoading(true);

      const dialCode = userData.countryCode;
      const phoneWithoutDialCode = userData.phone.startsWith(dialCode)
        ? userData.phone.slice(dialCode.length)
        : userData.phone;

      const submissionData = {
        ...userData,
        email: userData?.email?.trim()?.toLowerCase(),
        phone: phoneWithoutDialCode,
        countryCode: dialCode,
        isEligible: userData.isEligible === "true",
      };

      const response = await register(submissionData);
      if (response?.success) {
        navigate(`/signup/personal-info?userId=${response.user.userId}`);
      } else {
        toast.error(
          response?.message || "Registration failed. Please try again."
        );
      }
    } catch (error) {
      toast.error(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong. Please try again."
      );
      console.error("Error during registration:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="flex flex-col items-center justify-center gap-6"
    >
      <div className="flex flex-col w-full gap-2">
        <label htmlFor="name" className="text-sm font-medium text-[#374151]">
          Full Name
        </label>
        <input
          type="text"
          name="name"
          id="name"
          value={userData.name}
          onChange={userDataHandler}
          className=" border border-[#D1D5DB] py-2 px-3 rounded-md focus:ring-indigo-500 focus:border-indigo-500 focus:outline-none "
          placeholder="John Doe"
          required
        />
      </div>
      <div className="flex flex-col w-full gap-2">
        <label htmlFor="email" className="text-sm font-medium text-[#374151]">
          Email Address
        </label>
        <input
          type="email"
          name="email"
          id="email"
          value={userData.email}
          onChange={userDataHandler}
          className="border border-[#D1D5DB] py-2 px-3 rounded-md  focus:ring-indigo-500 focus:border-indigo-500 focus:outline-none "
          placeholder="<EMAIL>"
          required
        />
      </div>
      <div className="flex flex-col w-full gap-2">
        <label
          htmlFor="password"
          className="text-sm font-medium text-[#374151]"
        >
          Password
        </label>
        <div className="relative">
          <input
            type={showPassword ? "text" : "password"}
            name="password"
            id="password"
            value={userData.password}
            onChange={userDataHandler}
            className="border border-[#D1D5DB] py-2 px-3 rounded-md focus:ring-indigo-500 focus:border-indigo-500 focus:outline-none w-full"
            placeholder="*********"
            required
          />
          <button
            type="button"
            className="absolute right-3 top-1/2 -translate-y-1/2"
            onClick={() => setShowPassword((prev) => !prev)}
            tabIndex={-1}
            aria-label={showPassword ? "Hide password" : "Show password"}
          >
            <img
              src={
                showPassword
                  ? "/assets/icons/vis.svg"
                  : "/assets/icons/not_vis.svg"
              }
              alt={showPassword ? "Hide password" : "Show password"}
              className="w-5 h-5"
            />
          </button>
        </div>
      </div>
      <div className="flex flex-col w-full gap-2">
        <label htmlFor="phone" className="text-sm font-medium text-[#374151]">
          Phone
        </label>
        <PhoneInput
          country={"us"}
          value={userData.phone}
          onChange={mobileInputHandler}
          countries={countriesWithPhoneNumberLength}
          required={true}
          name="phone"
          id="phone"
          enableSearch={true}
          className="w-[100%] phone-input"
          inputStyle={{ height: "42px", width: "100%" }}
          placeholder="Enter your phone number"
        />
      </div>
      <div className="flex flex-col w-full gap-2">
        <label
          htmlFor="linkedin"
          className="text-sm font-medium text-[#374151]"
        >
          Linkedin Profile
        </label>
        <input
          type="url"
          name="linkedinUrl"
          id="linkedin"
          value={userData.linkedinUrl}
          onChange={userDataHandler}
          className="w-full border border-[#D1D5DB] py-2 px-3 rounded-md  focus:ring-indigo-500 focus:border-indigo-500 focus:outline-none "
          placeholder="https://linkedin.com/in/username"
          required
        />
      </div>
      <div className="flex flex-col w-full gap-2">
        <label
          htmlFor="isEligible"
          className="text-sm font-medium text-[#374151]"
        >
          Do you have minimum 1 year of experience in Healthcare or IT
          recruitment?
        </label>
        <div className="flex gap-4">
          <label className="flex items-center gap-2">
            <input
              type="radio"
              name="isEligible"
              value="true"
              checked={userData.isEligible === "true"}
              onChange={(e) =>
                setUserData({ ...userData, isEligible: e.target.value })
              }
              className="cursor-pointer"
            />
            Yes
          </label>
          <label className="flex items-center gap-2">
            <input
              type="radio"
              name="isEligible"
              value="false"
              checked={userData.isEligible === "false"}
              onChange={(e) =>
                setUserData({ ...userData, isEligible: e.target.value })
              }
              className="cursor-pointer"
            />
            No
          </label>
        </div>
      </div>
      <div className="flex  w-full gap-2 items-center">
        <input
          type="checkbox"
          name="terms"
          id="terms"
          checked={termsAccepted}
          onChange={(e) => setTermsAccepted(e.target.checked)}
          className="w-5 h-5 cursor-pointer"
        />
        <label htmlFor="terms" className="text-sm font-medium text-[#374151]">
          I agree to the{" "}
          <span className="text-indigo-600 cursor-pointer">
            <a href="https://hirring.com/terms-and-conditions" target="_blank">
              Terms & Conditions
            </a>
          </span>{" "}
          of Hirring.com
        </label>
      </div>
      <button
        type="submit"
        className={`w-full py-2 px-3 rounded-md ${
          isFormValid()
            ? "bg-[#65FF00] text-[#102108] font-medium cursor-pointer"
            : "bg-gray-300 text-gray-500 cursor-not-allowed"
        }`}
        disabled={!isFormValid() || loading}
      >
        {loading ? "Signing Up..." : "Sign Up"}
      </button>
      <div className="w-full flex justify-center items-center gap-1">
        Already have an account?
        <Link
          to={"/login"}
          className="text-sm text-blue-600 underline hover:text-blue-800 "
        >
          Sign In
        </Link>
      </div>
    </form>
  );
};

export default SignUpForm;
