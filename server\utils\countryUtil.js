const countries = require("./countries.json"); // This should point to server-side copy

// Normalize all valid country names to lowercase + space
const validCountries = countries.map(c =>
  c.name.toLowerCase().replace(/-/g, " ")
);

// Get all valid state names from all countries
const validStates = [];
countries.forEach(country => {
  if (country.states && country.states.length > 0) {
    country.states.forEach(state => {
      validStates.push(state.name.toLowerCase().replace(/-/g, " "));
    });
  }
});

// Takes input like "united-states" and returns normalized "united states" if valid
function normalizeCountry(input) {
  const cleaned = input.toLowerCase().replace(/-/g, " ").trim();
  return validCountries.includes(cleaned) ? cleaned : null;
}

// Takes input like "new-york" and returns normalized "new york" if valid
function normalizeState(input) {
  const cleaned = input.toLowerCase().replace(/-/g, " ").trim();
  return validStates.includes(cleaned) ? cleaned : null;
}

module.exports = {
  normalizeCountry,
  normalizeState,
  validCountries,
  validStates,
};
