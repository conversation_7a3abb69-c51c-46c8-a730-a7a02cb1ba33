const Job = require("../models/Job.models");
const jobSubmission = require("../models/CandidateSubmission.models");
const mongoose = require("mongoose");

const { readFileFromArrayBuffer } = require("../utils/xlsxCsvReader");
const { createID } = require("../utils");
const job = require("../models/Job.models");
const CoinTransaction = require("../models/CoinTransaction");
const { createNotification } = require("../services/notificationService");
const { normalizeCountry } = require("../utils/countryUtil");
const {
  buildSearchConditions,
  buildFilterConditions,
  buildSortConditions,
  buildBaseMatchConditions,
  executeJobAggregation,
  executeComplexJobAggregation,
  combineConditions,
  parseFilterArray,
} = require("../utils/jobQueryUtils");

//* @Desc create job
//* @Route POST /api/v1/job/create-job
//* @Access private, rolebased
const createJob = async (req, res) => {
  try {
    const {
      jobTitle,
      domain,
      commissionAmount,
      commissionCurrency,
      jobCountry,
      jobCity,
      jobState,
      JobZipCode,
      experience,
      jobStatus,
      jobType,
      remote,
      requiredHoursPerWeek,
      priority,
      openings,
      salary,
      clinetName,
      payRate,
      primarySkills,
      benefits,
      jobDescription,
      jobID,
      guaranteePeriod,
      jobProfile,
    } = req.body;

    const missingFields = Object.entries({
      jobTitle,
      jobCountry,
      domain,
      jobCity,
      commissionAmount,
      commissionCurrency,
      jobState,
      min: experience.min,
      max: experience.max,
      unit: experience.unit,
      jobStatus,
      jobType,
      priority,
      openings,
      benefits,
      primarySkills,
      jobDescription,
      minSalary: salary.min,
      maxSalary: salary.max,
      currencySalary: salary.currency,
      guaranteePeriod,
      jobProfile,
    })
      .filter(([key, value]) => {
        if (Array.isArray(value)) return value.length === 0;
        return value === undefined || value === null || value === "";
      })
      .map(([key]) => key);

    if (missingFields.length > 0) {
      return res.status(400).json({
        success: false,
        error: `Missing or invalid required field(s): ${missingFields.join(
          ", "
        )}`,
      });
    }

    if (jobType == "contract" && !payRate) {
      return res.status(400).json({
        success: false,
        error: `Contract pay rate is required.`,
      });
    }

    // count job exist
    const countJobs = await Job.countDocuments();

    const newJob = new Job({
      jobId: `HJ${new Date().getFullYear()}${createID(countJobs, 1)}`,
      jobTitle: jobTitle,
      industry: domain,
      externalJobId: jobID ? jobID?.toString() : "",
      commission: {
        amount: +commissionAmount,
        currency: commissionCurrency || "USD",
      },

      location: {
        country: jobCountry,
        city: jobCity || "",
        state: jobState || "",
        zipCode: JobZipCode || "",
      },

      experience: {
        min: +experience.min,
        max: +experience.max,
        unit: experience.unit || "years",
      },

      jobStatus: jobStatus || "open",
      jobType: jobType || "full-time",
      remote: remote || false,
      requiredHoursPerWeek: +requiredHoursPerWeek || 40,
      priority: priority || "low priority",
      openings: openings || 1,

      salary: {
        min: salary.min,
        max: salary.max,
        currency: salary.currency || "USD",
      },
      jobProfile: jobProfile,
      payRate: jobType == "contract" ? payRate : null,
      clientname: clinetName || "",
      primarySkills: primarySkills
        ? primarySkills?.filter((item) => item)?.map((skill) => skill?.trim())
        : [],
      benefits: benefits
        ? benefits?.filter((item) => item)?.map((benefit) => benefit?.trim())
        : [],
      jobDescription: jobDescription || "",
      guaranteePeriod: +guaranteePeriod || 60,
    });
    const createJobs = await newJob.save();
    res.status(200).json({ success: true, data: createJobs });
  } catch (err) {
    console.error("Error in creatingJob:", err);
    res.status(500).json({
      success: false,
      message: "Something went wrong while creating job",
      error: err.message,
    });
  }
};

//* @Desc head Manager will  upload jobs with xls file
//* @Route POST /api/v1/job/bulkupload
//* @Access private
const bulkUpload = async (req, res) => {
  try {
    const file = req?.file;

    // check file exist
    if (!file) {
      return res
        .status(400)
        .json({ success: false, message: "file not found" });
    }

    const fileType = file?.originalname?.split(".")[1];
    const fileData = file?.buffer;

    // check file data & name correct
    if (!(fileType && fileData)) {
      return res
        .status(400)
        .json({ success: false, message: "file parshing got error" });
    }

    const parshData = await readFileFromArrayBuffer(
      file.buffer,
      file.originalname?.split(".")[1]
    ).catch((error) => {
      console.error("Error:", error);
    });

    // check file read have some data
    if (parshData?.length < 1) {
      return res
        .status(400)
        .json({ success: false, message: "file uploaded data not found" });
    }

    // count job exist
    const countJobs = await Job.countDocuments();

    // check upload file have some missing value
    const checkValid = parshData?.filter((item) => {
      const isMissingRequiredFields = !(
        item.jobTitle &&
        item.minSalary &&
        item.maxSalary &&
        item.salaryCurrency &&
        item.country &&
        item.state &&
        item.city &&
        item.jobStatus &&
        item.jobType &&
        item.jobpriority &&
        item.minexperience &&
        item.maxexperience &&
        item["experienceunit(year/month)"] &&
        item.primarySkills &&
        item.jobopenings &&
        item.jobDescription &&
        item.benefits &&
        item?.guaranteePeriod &&
        item?.jobProfile
      );

      const isMissingPayRateForContract =
        item.jobType === "contract" && !item.payRate;
      return isMissingRequiredFields || isMissingPayRateForContract;
    });

    if (checkValid.length > 0) {
      return res.status(400).json({
        success: false,
        message: "file upload have some missing value.",
      });
    }

    // map data with module name
    const mapData = parshData?.map((data, key) => {
      return {
        jobId: `HJ${new Date().getFullYear()}${createID(countJobs, key + 1)}`,
        jobTitle: data.jobTitle,
        industry: data.industry,
        externalJobId: data?.externalJobID
          ? data?.externalJobID?.toString()
          : "",
        commission: {
          amount: data.commissionAmount,
          currency: data.commissionCurrency || "USD",
        },

        location: {
          country: data.country,
          state: data.state || "",
          city: data.city || "",
          zipCode: data.zipCode || "",
        },

        experience: {
          min: data.minexperience,
          max: data.maxexperience,
          unit: data["experienceunit(year/month)"] || "years",
        },

        jobStatus: data.jobStatus || "open",
        jobType: data.jobType || "full-time",
        remote: data.remote == "yes" ? true : false || false,
        requiredHoursPerWeek: data.requiredHoursPerWeek || 40,
        priority: data.jobpriority || "low priority",
        openings: data.jobopenings || 1,

        salary: {
          min: data.minSalary,
          max: data.maxSalary,
          currency: data.salaryCurrency || "USD",
        },

        payRate: data.jobType == "contract" ? data?.payRate : null,
        clientname: data?.client || "",
        primarySkills: data.primarySkills
          ? data.primarySkills.split(",").map((skill) => skill.trim())
          : [],
        benefits: data.benefits
          ? data.benefits.split(",").map((benefit) => benefit.trim())
          : [],
        jobDescription: data.jobDescription || "",
        guaranteePeriod: data?.guaranteePeriod,
        jobProfile: data?.jobProfile,
      };
    });

    // mutliple job create
    const resData = await Job.insertMany(mapData);

    return res.status(201).send({
      success: true,
      message: "bulk job create",
      data: resData,
    });
  } catch (error) {
    console.log("error in bulk upload", error);
    return res.status(400).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

//* @Desc head Manager will  assign to manager
//* @Route POST /api/v1/job/jobassigntomanager
//* @Access private
const jobAssignToManager = async (req, res) => {
  try {
    const { mapper } = req.body;
    const user = req.user;

    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    if (!mapper && mapper.length < 1) {
      res
        .status(200)
        .json({ success: false, message: "Please fill all required fields." });
    }

    const isValidFields = mapper.filter(
      (item) => !(item.jobID && item.managerID && item.systemGenratedID)
    );

    if (isValidFields.length > 0) {
      res
        .status(200)
        .json({ success: false, message: "Please fill all required fields." });
    }

    const jobIds = [];
    const managerIds = [];
    async function proccess(next) {
      if (next == mapper.length) {
        return;
      }

      const item = mapper[next];
      try {
        if (!item.directPublish) {
          await Job.findOneAndUpdate(
            { jobId: item.jobID },
            {
              accountManager: {
                _id: item.managerID,
                userID: item.systemGenratedID,
                assignAt: new Date(),
              },
            },
            {
              new: true,
            }
          );

          // add job for notifications
          jobIds.push(item.jobID);
          managerIds.push(item.managerID);
        } else {
          await Job.findOneAndUpdate(
            { jobId: item.jobID },
            {
              $set: {
                accountManager: {
                  _id: item.managerID,
                  userID: item.systemGenratedID,
                  assignAt: new Date(),
                },
                visibility: true,
              },
              $push: {
                updateOn: {
                  _id: new mongoose.Types.ObjectId(user._id),
                  userID: user.userID,
                },
              },
            }
          );

          // add job for notifications
          jobIds.push(item.jobID);
          managerIds.push(item.managerID);
        }
      } catch (error) {
        console.log(error);
        throw error;
      }
      await proccess(next + 1);
    }

    await proccess(0);

    // create notifications
    await createNotification({
      recipients: [...new Set(managerIds)],
      type: "job-assigned",
      relatedJobId: [...new Set(jobIds)],
    });

    // return
    return res.status(200).send({
      success: true,
      message: "assign to managers",
    });
  } catch (error) {
    console.log("job assign to manager", error);
    return res.status(400).json({
      success: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

//* @Desc fetch list of job
//* @Route GET /api/v1/job/getalljobs || /api/v1/job//am/getalljobs
//* @Access private headaccountmanager, account manager
const getAllJobs = async (req, res) => {
  try {
    const user = req?.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const page = parseInt(req.query.page) || null;
    const limit = parseInt(req.query.limit) || null;

    // Build base match conditions
    let baseMatch = buildBaseMatchConditions(user);

    // Add search conditions
    const searchConditions = buildSearchConditions(
      req.query.search,
      req.query.searchField
    );
    Object.assign(baseMatch, searchConditions);

    // Add filter conditions (only if search doesn't override them)
    if (!searchConditions.$or) {
      const filterConditions = buildFilterConditions({
        locations: req.query.locations,
        states: req.query.states,
        jobTypes: req.query.jobTypes,
        jobStatus: req.query.jobStatus,
        visibility: req.query.visibility,
        jobPosted: req.query.jobPosted,
        numberOfPosition: req.query.numberOfPosition,
        salaryRange: req.query.salaryRange,
      });
      Object.assign(baseMatch, filterConditions);
    }

    // Build sort conditions
    const sortConditions = buildSortConditions(
      req.query.postedDate,
      req.query.sortBy,
      req.query.sortOrder
    );

    // Execute aggregation
    const { jobs, total, totalPages } = await executeJobAggregation(
      job,
      baseMatch,
      sortConditions,
      page,
      limit
    );

    return res.status(200).json({
      success: true,
      message: "Get all jobs",
      results: jobs,
      total,
      page,
      totalPages,
    });
  } catch (err) {
    console.error("Error in getAllJobs:", err);
    res.status(500).json({
      success: false,
      message: "Something went wrong while fetching jobs",
      error: err.message,
    });
  }
};

//* @Desc fetch list of unassigned jobs
//* @Route GET /api/v1/job/getunassignedjobs
//* @Access private headaccountmanager
// const getAllUnassignedJobs = async (req, res) => {
//   try {
//     const allUnassignedJobs = await job
//       .find({ accountManager: { $exists: false }, isDeleted: false })
//       .sort({ updatedAt: -1 });

//     return res.status(200).send({
//       success: true,
//       message: "get all unassigned jobs",
//       data: allUnassignedJobs,
//     });
//   } catch (err) {
//     console.error("Error in getAllJobs:", err);
//     res.status(400).json({
//       success: false,
//       message: "Something went wrong while fetching jobs",
//       error: err.message,
//     });
//   }
// };

//* @Desc fetch list of unassigned jobs
//* @Route GET /api/v1/job/getunassignedjobs
//* @Access private headaccountmanager

const getAllUnassignedJobs = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    // Build base match conditions for unassigned jobs
    let baseMatch = buildBaseMatchConditions(null, "unassigned");

    // Add search conditions
    const searchConditions = buildSearchConditions(
      req.query.search,
      req.query.searchField
    );
    Object.assign(baseMatch, searchConditions);

    // Add filter conditions (only if search doesn't override them)
    if (!searchConditions.$or) {
      const filterConditions = buildFilterConditions({
        specialization: req.query.specialization,
        jobTypes: req.query.jobType,
      });
      Object.assign(baseMatch, filterConditions);
    }

    // Build sort conditions
    const sortConditions = buildSortConditions(
      req.query.postedDate,
      req.query.sortBy,
      req.query.sortOrder
    );

    // Execute aggregation
    const { jobs, total, totalPages } = await executeJobAggregation(
      job,
      baseMatch,
      sortConditions,
      page,
      limit
    );

    return res.status(200).json({
      success: true,
      message: "Successfully retrieved unassigned jobs",
      results: jobs,
      total,
      page: Math.max(1, page),
      totalPages,
      limit,
      appliedFilters: {
        specialization: parseFilterArray(req.query.specialization),
        jobType: parseFilterArray(req.query.jobType),
      },
      appliedSorting: {
        sortBy: req.query.sortBy || "updatedAt",
        sortOrder: req.query.sortOrder || "desc",
        postedDate: req.query.postedDate || null,
        sortObject: sortConditions,
      },
    });
  } catch (err) {
    console.error("Error in getAllUnassignedJobs:", err);

    return res.status(500).json({
      success: false,
      message: "Something went wrong while fetching unassigned jobs",
      error:
        process.env.NODE_ENV === "development"
          ? err.message
          : "Internal server error",
    });
  }
};

//* @Desc fetch list of high Periority jobs
//* @Route GET /api/v1/job/getHighPeriorityJobs || /api/v1/job/am/gethighperiorityjobs
//* @Access private headaccountmanager, account manager
// const getHighPeriorityJobs = async (req, res) => {
//   try {
//     const user = req?.user;
//     if (!user) {
//       res.status(400).json({ success: false, message: "user not found" });
//     }

//     let findQuery = {
//       priority: "high priority",
//       isDeleted: false,
//     };
//     // check if user is account manager then associate job visible
//     if (user.userType == "accountManager") {
//       findQuery = {
//         "accountManager.userID": user.userId,
//         isDeleted: false,
//         priority: "high priority",
//       };
//     }

//     const allHighPeriorityJobs = await job.aggregate([
//       {
//         $match: findQuery,
//       },
//       {
//         $sort: {
//           createdAt: -1,
//         },
//       },
//       {
//         $lookup: {
//           from: "cointransactions",
//           localField: "_id",
//           foreignField: "relatedJobId",
//           as: "recruiterRequest",
//         },
//       },
//       {
//         $lookup: {
//           from: "workrequests",
//           let: { jobID: "$jobId" },
//           pipeline: [
//             {
//               $match: {
//                 $expr: {
//                   $and: [
//                     {
//                       $eq: ["$job.jobId", "$$jobID"],
//                     },
//                     {
//                       $eq: ["$status", "accepted"],
//                     },
//                   ],
//                 },
//               },
//             },
//           ],
//           as: "workOnRequest",
//         },
//       },
//       {
//         $lookup: {
//           from: "candidatesubmissions",
//           localField: "_id",
//           foreignField: "job._id",
//           as: "submissions",
//         },
//       },
//       {
//         $addFields: {
//           recruiterCount: {
//             $sum: [
//               {
//                 $size: "$workOnRequest",
//               },
//               {
//                 $size: "$recruiterRequest",
//               },
//             ],
//           },
//           submissionsCount: {
//             $size: "$submissions",
//           },
//         },
//       },
//     ]);

//     return res.status(200).send({
//       success: true,
//       message: "get all high Periority jobs",
//       data: allHighPeriorityJobs,
//     });
//   } catch (err) {
//     console.error("Error in getAllJobs:", err);
//     res.status(400).json({
//       success: false,
//       message: "Something went wrong while fetching jobs",
//       error: err.message,
//     });
//   }
// };

const getHighPeriorityJobs = async (req, res) => {
  try {
    const user = req?.user;
    if (!user) {
      return res
        .status(400)
        .json({ success: false, message: "user not found" });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    // Build base match conditions for high priority jobs
    let baseMatch = buildBaseMatchConditions(user, "high-priority");

    // Add search conditions
    const searchConditions = buildSearchConditions(
      req.query.search,
      req.query.searchField
    );

    // Add filter conditions
    const filterConditions = buildFilterConditions({
      locations: req.query.locations,
      states: req.query.states,
      jobTypes: req.query.jobTypes,
      jobPosted: req.query.jobPosted,
      numberOfPosition: req.query.numberOfPosition,
      salaryRange: req.query.salaryRange,
    });

    // Combine all conditions
    const finalMatch = combineConditions(
      baseMatch,
      searchConditions,
      filterConditions
    );

    // Build sort conditions
    const sortConditions = buildSortConditions(
      req.query.postedDate,
      req.query.sortBy,
      req.query.sortOrder
    );
    const additionalStages = [
      {
        $lookup: {
          from: "cointransactions",
          localField: "_id",
          foreignField: "relatedJobId",
          as: "recruiterRequest",
        },
      },
      {
        $lookup: {
          from: "workrequests",
          let: { jobID: "$jobId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$job.jobId", "$$jobID"] },
                    { $eq: ["$status", "accepted"] },
                  ],
                },
              },
            },
          ],
          as: "workOnRequest",
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          localField: "_id",
          foreignField: "job._id",
          as: "submissions",
        },
      },
      {
        $addFields: {
          recruiterCount: {
            $sum: [
              { $size: { $ifNull: ["$workOnRequest", []] } },
              { $size: { $ifNull: ["$recruiterRequest", []] } },
            ],
          },
          submissionsCount: { $size: { $ifNull: ["$submissions", []] } },
        },
      },
    ];

    // Execute aggregation
    const { jobs, total, totalPages } = await executeJobAggregation(
      job,
      finalMatch,
      sortConditions,
      page,
      limit,
      additionalStages
    );

    return res.status(200).json({
      success: true,
      message: "Successfully retrieved high priority jobs",
      results: jobs,
      total,
      totalPages,
      page,
      appliedFilters: {
        locations: parseFilterArray(req.query.locations),
        states: parseFilterArray(req.query.states),
        jobTypes: parseFilterArray(req.query.jobTypes),
        jobPosted: parseFilterArray(req.query.jobPosted),
        numberOfPosition: parseFilterArray(req.query.numberOfPosition),
        salaryRange: parseFilterArray(req.query.salaryRange),
        search: req.query.search || null,
        searchField: req.query.searchField || null,
      },
      appliedSorting: {
        sortBy: req.query.sortBy || "createdAt",
        sortOrder: req.query.sortOrder || "desc",
        postedDate: req.query.postedDate || null,
        sortObject: sortConditions,
      },
    });
  } catch (error) {
    console.error("Error in getHighPriorityJobs:", error);

    return res.status(500).json({
      success: false,
      message: "Error while fetching high priority jobs",
      error:
        process.env.NODE_ENV === "development"
          ? error.message
          : "Internal server error",
    });
  }
};

//* @Desc fetch list of Active jobs
//* @Route GET /api/v1/job/getactivejobs ||  /api/v1/job/am/getactivejobs
//* @Access private headaccountmanager, account manager
const getActiveJobs = async (req, res) => {
  try {
    const user = req?.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    // Build base match conditions for active jobs
    let baseMatch = buildBaseMatchConditions(user, "active");

    // Add search conditions
    const searchConditions = buildSearchConditions(
      req.query.search,
      req.query.searchField
    );

    // Add filter conditions
    const filterConditions = buildFilterConditions({
      locations: req.query.location,
      states: req.query.state,
      jobTypes: req.query.jobType,
      visibility: req.query.visibility,
      jobPosted: req.query.jobPosted,
      numberOfPosition: req.query.numberOfPosition,
      salaryRange: req.query.salaryRange,
    });

    // Combine all conditions
    const finalMatch = combineConditions(
      baseMatch,
      searchConditions,
      filterConditions
    );

    // Build sort conditions
    const sortConditions = buildSortConditions(
      req.query.postedDate,
      req.query.sortBy,
      req.query.sortOrder
    );

    // Additional aggregation stages for active jobs
    const additionalStages = [
      {
        $lookup: {
          from: "cointransactions",
          localField: "_id",
          foreignField: "relatedJobId",
          as: "recruiterRequest",
        },
      },
      {
        $lookup: {
          from: "workrequests",
          let: { jobID: "$jobId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$job.jobId", "$$jobID"] },
                    { $eq: ["$status", "accepted"] },
                  ],
                },
              },
            },
          ],
          as: "workOnRequest",
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          localField: "_id",
          foreignField: "job._id",
          as: "submissions",
        },
      },
      {
        $addFields: {
          recruiterCount: {
            $sum: [
              { $size: { $ifNull: ["$workOnRequest", []] } },
              { $size: { $ifNull: ["$recruiterRequest", []] } },
            ],
          },
          submissionsCount: { $size: { $ifNull: ["$submissions", []] } },
        },
      },
    ];

    // Execute aggregation
    const { jobs, total, totalPages } = await executeJobAggregation(
      job,
      finalMatch,
      sortConditions,
      page,
      limit,
      additionalStages
    );

    return res.status(200).json({
      success: true,
      message: "Successfully retrieved active jobs",
      results: jobs,
      page: Math.max(1, page),
      total,
      totalPages,
      limit,
      appliedFilters: {
        jobType: parseFilterArray(req.query.jobType),
        location: parseFilterArray(req.query.location),
        state: parseFilterArray(req.query.state),
        visibility: parseFilterArray(req.query.visibility),
        jobPosted: parseFilterArray(req.query.jobPosted),
        numberOfPosition: parseFilterArray(req.query.numberOfPosition),
        salaryRange: parseFilterArray(req.query.salaryRange),
        search: req.query.search || null,
        searchField: req.query.searchField || null,
      },
      appliedSorting: {
        sortBy: req.query.sortBy || "updatedAt",
        sortOrder: req.query.sortOrder || "desc",
        postedDate: req.query.postedDate || null,
        sortObject: sortConditions,
      },
    });
  } catch (err) {
    console.error("Error in getActiveJobs:", err);

    return res.status(500).json({
      success: false,
      message: "Something went wrong while fetching active jobs",
      error:
        process.env.NODE_ENV === "development"
          ? err.message
          : "Internal server error",
    });
  }
};

//* @Desc fetch list of close jobs
//* @Route GET /api/v1/job/getclosejobs || /api/v1/job/am/getclosejobs
//* @Access private headaccountmanager, account manager
const getCloseJobs = async (req, res) => {
  try {
    const user = req?.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    // Build base match conditions for closed jobs
    let baseMatch = buildBaseMatchConditions(user, "closed");

    // Add search conditions
    const searchConditions = buildSearchConditions(
      req.query.search,
      req.query.searchField
    );

    // Add filter conditions with special handling for job status
    const filterConditions = buildFilterConditions({
      locations: req.query.locations,
      states: req.query.states,
      jobTypes: req.query.jobTypes,
      jobStatus: req.query.jobStatus,
      jobPosted: req.query.jobPosted,
      numberOfPosition: req.query.numberOfPosition,
      salaryRange: req.query.salaryRange,
      isClosedJobFilter: true, // Special flag for closed job status filtering
    });

    // Combine all conditions
    const finalMatch = combineConditions(
      baseMatch,
      searchConditions,
      filterConditions
    );

    // Build sort conditions (default to createdAt for closed jobs)
    const sortConditions = buildSortConditions(
      req.query.postedDate,
      req.query.sortBy || "createdAt",
      req.query.sortOrder
    );

    // Additional aggregation stages for closed jobs
    const additionalStages = [
      {
        $lookup: {
          from: "cointransactions",
          localField: "_id",
          foreignField: "relatedJobId",
          as: "recruiterRequest",
        },
      },
      {
        $lookup: {
          from: "workrequests",
          let: { jobID: "$jobId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$job.jobId", "$$jobID"] },
                    { $eq: ["$status", "accepted"] },
                  ],
                },
              },
            },
          ],
          as: "workOnRequest",
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          localField: "_id",
          foreignField: "job._id",
          as: "submissions",
        },
      },
      {
        $addFields: {
          recruiterCount: {
            $sum: [{ $size: "$workOnRequest" }, { $size: "$recruiterRequest" }],
          },
          submissionsCount: { $size: "$submissions" },
        },
      },
    ];

    // Execute aggregation
    const { jobs, total, totalPages } = await executeJobAggregation(
      job,
      finalMatch,
      sortConditions,
      page,
      limit,
      additionalStages
    );

    return res.status(200).json({
      success: true,
      message: "Successfully retrieved closed jobs",
      results: jobs,
      page,
      total,
      totalPages,
      appliedFilters: {
        locations: parseFilterArray(req.query.locations),
        states: parseFilterArray(req.query.states),
        jobTypes: parseFilterArray(req.query.jobTypes),
        jobStatus: parseFilterArray(req.query.jobStatus),
        jobPosted: parseFilterArray(req.query.jobPosted),
        numberOfPosition: parseFilterArray(req.query.numberOfPosition),
        salaryRange: parseFilterArray(req.query.salaryRange),
        search: req.query.search || null,
        searchField: req.query.searchField || null,
      },
      appliedSorting: {
        sortBy: req.query.sortBy || "createdAt",
        sortOrder: req.query.sortOrder || "desc",
        postedDate: req.query.postedDate || null,
        sortObject: sortConditions,
      },
    });
  } catch (err) {
    console.error("Error in getClosedJobs:", err);

    return res.status(500).json({
      success: false,
      message: "Something went wrong while fetching closed jobs",
      error:
        process.env.NODE_ENV === "development"
          ? err.message
          : "Internal server error",
    });
  }
};

//* @Desc fetch list of un-engaged jobs
//* @Route GET /api/v1/job/getunengagedjobs || /api/v1/job/am/getunengagedjobs
//* @Access private
const getUnEngagedJobs = async (req, res) => {
  try {
    const user = req?.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    // Build base match conditions for unengaged jobs
    let baseMatch = buildBaseMatchConditions(user, "unengaged");

    // Add search conditions
    const searchConditions = buildSearchConditions(
      req.query.search,
      req.query.searchField
    );

    // Add filter conditions
    const filterConditions = buildFilterConditions({
      locations: req.query.location,
      states: req.query.state,
      jobTypes: req.query.jobType,
      jobPosted: req.query.jobPosted,
      numberOfPosition: req.query.numberOfPosition,
      salaryRange: req.query.salaryRange,
    });

    // Combine all conditions
    const finalMatch = combineConditions(
      baseMatch,
      searchConditions,
      filterConditions
    );

    // Build sort conditions (default to createdAt for unengaged jobs)
    const sortConditions = buildSortConditions(
      req.query.postedDate,
      req.query.sortBy || "createdAt",
      req.query.sortOrder
    );

    // Additional aggregation stages for unengaged jobs
    const additionalStages = [
      {
        $lookup: {
          from: "workrequests",
          let: { jobID: "$jobId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$job.jobId", "$$jobID"] },
                    { $eq: ["$status", "accepted"] },
                  ],
                },
              },
            },
          ],
          as: "workOnRequest",
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          localField: "_id",
          foreignField: "job._id",
          as: "submission",
        },
      },
      {
        $addFields: {
          workOnRequestCount: { $size: "$workOnRequest" },
          isSubmission: { $size: "$submission" },
        },
      },
    ];

    // Final match conditions to filter only unengaged jobs
    const finalMatchConditions = {
      $expr: {
        $and: [{ $eq: ["$isSubmission", 0] }],
      },
    };

    // Execute complex aggregation with final match
    const { jobs, total, totalPages } = await executeComplexJobAggregation(
      job,
      finalMatch,
      sortConditions,
      page,
      limit,
      additionalStages,
      finalMatchConditions
    );

    // Add workOnRequestCount field to final results
    const jobsWithCount = jobs.map((job) => ({
      ...job,
      workOnRequestCount: 0, // Since these are unengaged jobs
    }));

    return res.status(200).json({
      success: true,
      message: "Successfully retrieved unengaged jobs",
      results: jobsWithCount,
      page: Math.max(1, page),
      total,
      totalPages,
      limit,
      appliedFilters: {
        jobType: parseFilterArray(req.query.jobType),
        location: parseFilterArray(req.query.location),
        state: parseFilterArray(req.query.state),
        jobPosted: parseFilterArray(req.query.jobPosted),
        numberOfPosition: parseFilterArray(req.query.numberOfPosition),
        salaryRange: parseFilterArray(req.query.salaryRange),
        search: req.query.search || null,
        searchField: req.query.searchField || null,
      },
      appliedSorting: {
        sortBy: req.query.sortBy || "createdAt",
        sortOrder: req.query.sortOrder || "desc",
        postedDate: req.query.postedDate || null,
        sortObject: sortConditions,
      },
    });
  } catch (err) {
    console.error("Error in getUnengagedJobs:", err);

    return res.status(500).json({
      success: false,
      message: "Something went wrong while fetching unengaged jobs",
      error:
        process.env.NODE_ENV === "development"
          ? err.message
          : "Internal server error",
    });
  }
};

//* @Desc get single job
//* @Route POST /api/v1/job/list
//* @Access Public
const getSingleJob = async (req, res) => {
  try {
    const { jobID } = req.params;

    const user = req?.user;
    if (!user) {
      res.status(400).json({ success: false, message: "user not found" });
    }

    if (!jobID) {
      res.status(400).josn({ success: false, message: "job Id not found" });
    }

    let findQuery = {
      jobId: jobID,
      isDeleted: false,
    };

    // If user type will be account manager
    if (user.userType == "accountManager") {
      findQuery = {
        isDeleted: false,
        jobId: jobID,
        "accountManager.userID": user.userId,
      };
    }

    let workingOn = [];
    // user type will recruiter then check job will be published
    if (user.userType == "recruiter") {
      findQuery = {
        isDeleted: false,
        visibility: true,
        jobId: jobID,
      };

      workingOn = [
        {
          $lookup: {
            from: "recruiters",
            let: { jobId: "$_id", userId: user.userId },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      {
                        $eq: ["$user.userId", "$$userId"],
                      },
                      {
                        $gt: [
                          {
                            $size: {
                              $filter: {
                                input: "$jobsWorkingOn",
                                as: "job",
                                cond: {
                                  $and: [
                                    {
                                      $eq: ["$$job.jobId", "$$jobId"],
                                    },
                                    {
                                      $eq: ["$$job.status", "assigned"],
                                    },
                                    {
                                      $eq: ["$$job.isActive", true],
                                    },
                                  ],
                                },
                              },
                            },
                          },
                          0,
                        ],
                      },
                    ],
                  },
                },
              },
            ],
            as: "workingOn",
          },
        },
        {
          $addFields: {
            isWorkingOn: {
              $gt: [{ $size: "$workingOn" }, 0],
            },
          },
        },
      ];
    }

    let jobSearch;

    // if user type account Manager, head Account Manager then we call get all submission details.
    if (["accountManager", "headAccountManager"].includes(user.userType)) {
      jobSearch = [
        {
          $match: findQuery,
        },
        {
          $lookup: {
            from: "candidatesubmissions",
            let: { jobIDs: "$jobId" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ["$job.jobID", "$$jobIDs"],
                  },
                },
              },
              {
                $lookup: {
                  from: "candidates",
                  localField: "candidate._id",
                  foreignField: "_id",
                  as: "candidate",
                },
              },
              {
                $lookup: {
                  from: "users",
                  localField: "submittedBy._id",
                  foreignField: "_id",
                  as: "submittedBy",
                },
              },
              {
                $unwind: {
                  path: "$submittedBy",
                  preserveNullAndEmptyArrays: true,
                },
              },
              {
                $unwind: {
                  path: "$candidate",
                  preserveNullAndEmptyArrays: true,
                },
              },
              {
                $project: {
                  candidate: 1,
                  submittedBy: 1,
                  status: 1,
                  submissionId: 1,
                },
              },
            ],

            as: "candidatesubmissions",
          },
        },

        {
          $lookup: {
            from: "candidatesubmissions",
            let: { jobID: "$_id" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ["$job._id", "$$jobID"],
                  },
                },
              },
              {
                $facet: {
                  candidate: [
                    {
                      $group: {
                        _id: "$status",
                        count: {
                          $sum: 1,
                        },
                      },
                    },
                    {
                      $project: {
                        status: "$_id",
                        count: "$count",
                        _id: false,
                      },
                    },
                  ],
                },
              },
            ],
            as: "submission",
          },
        },
        {
          $unwind: {
            path: "$submission",
            preserveNullAndEmptyArrays: false,
          },
        },

        {
          $lookup: {
            from: "recruiters",
            let: { jobID: "$_id" },
            pipeline: [
              {
                $unwind: {
                  path: "$jobsWorkingOn",
                  preserveNullAndEmptyArrays: false,
                },
              },

              {
                $match: {
                  "jobsWorkingOn.status": "assigned",
                  "jobsWorkingOn.isActive": true,
                },
              },
              {
                $group: {
                  _id: "$jobsWorkingOn.jobId",
                  count: {
                    $sum: 1,
                  },
                },
              },

              {
                $match: {
                  $expr: {
                    $and: [
                      {
                        $eq: ["$_id", "$$jobID"],
                      },
                    ],
                  },
                },
              },
            ],
            as: "recruiterCount",
          },
        },
        {
          $unwind: {
            path: "$recruiterCount",
            preserveNullAndEmptyArrays: true,
          },
        },

        {
          $lookup: {
            from: "users",
            let: { userIDs: "$accountManager.userID" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ["$userId", "$$userIDs"],
                  },
                },
              },

              {
                $project: {
                  name: 1,
                  userId: 1,
                  email: 1,
                  phone: 1,
                },
              },
            ],

            as: "accountManager",
          },
        },

        {
          $unwind: {
            path: "$accountManager",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "auditlogs",
            let: { jobId: "$_id" },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ["$JobId", "$$jobId"] },
                },
              },
              {
                $sort: {
                  createdAt: -1,
                },
              },
            ],
            as: "updates",
          },
        },
      ];

      // if user type will recruiter then submission will get all according to recruiter.
    } else {
      jobSearch = [
        {
          $match: findQuery,
        },
        ...workingOn,
        {
          $lookup: {
            from: "candidatesubmissions",
            let: { jobIDs: "$jobId", userID: user.userId },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ["$job.jobID", "$$jobIDs"] },
                      { $eq: ["$submittedBy.userID", "$$userID"] },
                    ],
                  },
                },
              },
              {
                $lookup: {
                  from: "candidates",
                  localField: "candidate._id",
                  foreignField: "_id",
                  as: "candidate",
                },
              },
              {
                $lookup: {
                  from: "users",
                  let: { userId: "$submittedBy._id" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: ["$_id", "$$userId"],
                        },
                      },
                    },
                    {
                      $project: {
                        _id: 1,
                        name: 1,
                        email: 1,
                        userId: 1,
                        phone: 1,
                        createdAt: 1,
                      },
                    },
                  ],

                  as: "submittedBy",
                },
              },
              {
                $unwind: {
                  path: "$submittedBy",
                  preserveNullAndEmptyArrays: true,
                },
              },
              {
                $unwind: {
                  path: "$candidate",
                  preserveNullAndEmptyArrays: true,
                },
              },
              {
                $project: {
                  candidate: 1,
                  submittedBy: 1,
                  status: 1,
                  submissionId: 1,
                },
              },
            ],

            as: "candidatesubmissions",
          },
        },

        {
          $lookup: {
            from: "users",
            let: { userIDs: "$accountManager.userID" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ["$userId", "$$userIDs"],
                  },
                },
              },

              {
                $project: {
                  name: 1,
                  userId: 1,
                  email: 1,
                  phone: 1,
                },
              },
            ],

            as: "accountManager",
          },
        },
        {
          $unwind: {
            path: "$accountManager",
            preserveNullAndEmptyArrays: true,
          },
        },

        {
          $lookup: {
            from: "candidatesubmissions",
            let: { jobID: "$_id" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ["$job._id", "$$jobID"],
                  },
                },
              },
              {
                $facet: {
                  candidate: [
                    {
                      $group: {
                        _id: "$status",
                        count: {
                          $sum: 1,
                        },
                      },
                    },
                    {
                      $project: {
                        status: "$_id",
                        count: "$count",
                        _id: false,
                      },
                    },
                  ],
                },
              },
            ],
            as: "submission",
          },
        },
        {
          $unwind: {
            path: "$submission",
            preserveNullAndEmptyArrays: false,
          },
        },

        {
          $lookup: {
            from: "recruiters",
            let: { jobID: "$_id" },
            pipeline: [
              {
                $unwind: {
                  path: "$jobsWorkingOn",
                  preserveNullAndEmptyArrays: false,
                },
              },

              {
                $match: {
                  "jobsWorkingOn.status": "assigned",
                  "jobsWorkingOn.isActive": true,
                },
              },
              {
                $group: {
                  _id: "$jobsWorkingOn.jobId",
                  count: {
                    $sum: 1,
                  },
                },
              },

              {
                $match: {
                  $expr: {
                    $and: [
                      {
                        $eq: ["$_id", "$$jobID"],
                      },
                    ],
                  },
                },
              },
            ],
            as: "recruiterCount",
          },
        },
        {
          $unwind: {
            path: "$recruiterCount",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "auditlogs",
            let: { jobId: "$_id", visible: "external" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ["$JobId", "$$jobId"] },
                      { $eq: ["$visibleTo", "$$visible"] },
                    ],
                  },
                },
              },
              {
                $sort: {
                  createdAt: -1,
                },
              },
            ],
            as: "updates",
          },
        },
      ];
    }

    let getJob = await Job.aggregate(jobSearch);

    return res.status(200).json({
      success: true,
      data: getJob,
      message: "Fetch job details",
    });
  } catch (err) {
    console.error("Error in getSingleJob:", err);
    res.status(500).json({
      success: false,
      message: "Something went wrong while fetching job",
      error: err.message,
    });
  }
};

//* @Desc update job
//* @Route POST /api/v1/job/update-job
//* @Access private, rolebased
const updatejob = async (req, res) => {
  try {
    //const { jobId } = req.params;
    const { jobId, data } = req.body;
    const user = req.user;

    if (!user) {
      return res.status(404).json({
        status: false,
        message: "user not found",
      });
    }

    if (!mongoose.Types.ObjectId.isValid(jobId)) {
      return res.status(500).json({
        message: "invalid job",
      });
    }

    const updatedJob = await Job.findByIdAndUpdate(
      jobId,
      { $set: data },
      { new: true, runValidators: true }
    );

    if (!updatedJob) {
      return res.status(404).json({
        message: "Job not found",
      });
    }

    res.status(200).json({
      message: "Job updated successfully",
      job: updatedJob,
    });
  } catch (err) {
    console.error("Update job failed:", err);
    res.status(500).json({
      success: false,
      message: "Update Failed",
      error: err.message,
    });
  }
};

//* @Desc remove job ***ARCHIVED Job**
//* @Route POST /api/v1/job/remove-job
//* @Access private, rolebased
const removeJob = async (req, res) => {
  try {
    req.user = { _id: "dummyUserId", name: "Test User" };
    const { jobId } = req.body;
    const user = req.user;

    if (!user) {
      return res.status(404).json({
        status: false,
        message: "user not found",
      });
    }

    if (!jobId)
      return res.status(404).json({
        message: "Job not found",
      });

    if (!mongoose.Types.ObjectId.isValid(jobId)) {
      return res.status(500).json({ message: "invalid job" });
    }

    const job = await Job.findById(jobId);

    if (job.isDeleted) {
      return res.status(400).json({
        message: "Job already deleted",
      });
    }

    job.isDeleted = true;
    const isDeletedStatus = await job.save();

    // const isDeletedStatus = await Job.findByIdAndUpdate(
    //   jobId,
    //   { isDeleted: true },
    //   { new: true }
    // );

    // console.log(isDeletedStatus);

    if (!isDeletedStatus) {
      return res.status(404).json({
        message: "Job not found",
      });
    }

    res.status(204).json({
      message: "Job deleted successfully",
    });
  } catch (err) {
    console.error("Update job failed:", err);
    res.status(500).json({
      message: "Update Failed",
      error: err.message,
    });
  }
};

//* @Desc account manager will publish the job
//* @Route POST /api/v1/job/publishJob
//* @Access private, AccountManager
const publishJob = async (req, res) => {
  try {
    const user = req?.user;

    if (!user) {
      res.status(400).json({ success: false, message: "user not found" });
    }
    const {
      jobID,
      jobTitle,
      externalJobID,
      domain,
      commissionAmount,
      commissionCurrency,
      jobCountry,
      jobCity,
      jobState,
      JobZipCode,
      experience,
      jobStatus,
      jobType,
      remote,
      requiredHoursPerWeek,
      priority,
      openings,
      salary,
      clinetName,
      payRate,
      primarySkills,
      benefits,
      jobDescription,
      guaranteePeriod,
      jobProfile,
    } = req.body;

    const missingFields = Object.entries({
      jobID,
      jobTitle,
      jobCountry,
      domain,
      jobCity,
      commissionAmount,
      commissionCurrency,
      jobState,
      min: experience.min,
      max: experience.max,
      unit: experience.unit,
      jobStatus,
      jobType,
      priority,
      openings,
      benefits,
      primarySkills,
      jobDescription,
      minSalary: salary.min,
      maxSalary: salary.max,
      currencySalary: salary.currency,
      jobProfile: jobProfile,
    })
      .filter(([key, value]) => {
        if (Array.isArray(value)) return value.length === 0;
        return value === undefined || value === null || value === "";
      })
      .map(([key]) => key);

    if (missingFields.length > 0) {
      return res.status(400).json({
        success: false,
        error: `Missing or invalid required field(s): ${missingFields.join(
          ", "
        )}`,
      });
    }

    if (jobType == "contract" && !payRate) {
      return res.status(400).json({
        success: false,
        error: `Contract pay rate is required.`,
      });
    }

    // if publish by account manager then
    if (user.userType == "accountManager") {
      const isJobExist = await Job.findOne({
        jobId: jobID,
        "accountManager._id": new mongoose.Types.ObjectId(user._id),
      });
      if (!isJobExist) {
        return res
          .status(400)
          .json({ success: false, message: "This job does not exist." });
      }
    } else {
      // check account manager is exist
      const isJobExist = await Job.findOne({
        jobId: jobID,
        accountManager: { $exists: true },
      });
      if (!isJobExist) {
        return res
          .status(400)
          .json({ success: false, message: "This job does not exist." });
      }
    }

    const updateJob = await Job.findOneAndUpdate(
      {
        jobId: jobID,
      },
      {
        $set: {
          jobTitle: jobTitle,
          industry: domain,
          externalJobId: externalJobID ? externalJobID : "",
          commission: {
            amount: commissionAmount,
            currency: commissionCurrency || "USD",
          },
          location: {
            country: jobCountry,
            city: jobCity || "",
            state: jobState || "",
            zipCode: JobZipCode || "",
          },
          experience: {
            min: experience.min,
            max: experience.max,
            unit: experience.unit || "years",
          },
          jobStatus: jobStatus || "open",
          jobType: jobType || "full-time",
          remote: remote || false,
          requiredHoursPerWeek: requiredHoursPerWeek || 40,
          priority: priority || "low priority",
          openings: openings || 1,
          salary: {
            min: salary.min,
            max: salary.max,
            currency: salary.currency || "USD",
          },
          payRate: jobType == "contract" ? payRate : null,
          clientname: clinetName || "",
          primarySkills: primarySkills
            ? primarySkills.filter((item) => item).map((skill) => skill.trim())
            : [],
          benefits: benefits
            ? benefits.filter((item) => item).map((benefit) => benefit.trim())
            : [],
          jobDescription: jobDescription || "",
          visibility: true,
          guaranteePeriod: guaranteePeriod || 60,
          jobProfile,
        },
        $push: {
          updateOn: {
            _id: new mongoose.Types.ObjectId(user._id),
            userID: user.userId,
          },
        },
      },
      { new: true }
    );

    if (!updateJob) {
      return res
        .send(400)
        .json({ success: false, message: "Job not found or failed to update" });
    }
    return res.status(200).json({ success: true, data: updateJob });
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, message: req?.message });
  }
};

//* @Desc head account manager will update the job
//* @Route POST /api/v1/job/updatejob
//* @Access private, AccountManager
const updateJob = async (req, res) => {
  try {
    const user = req?.user;

    if (!user) {
      res.status(400).json({ success: false, message: "user not found" });
    }
    const {
      jobID,
      jobTitle,
      externalJobID,
      domain,
      commissionAmount,
      commissionCurrency,
      jobCountry,
      jobCity,
      jobState,
      JobZipCode,
      experience,
      jobStatus,
      jobType,
      remote,
      requiredHoursPerWeek,
      priority,
      openings,
      salary,
      clinetName,
      payRate,
      primarySkills,
      benefits,
      jobDescription,
      guaranteePeriod,
      jobProfile,
    } = req.body;

    const missingFields = Object.entries({
      jobID,
      jobTitle,
      jobCountry,
      domain,
      jobCity,
      commissionAmount,
      commissionCurrency,
      jobState,
      min: experience.min,
      max: experience.max,
      unit: experience.unit,
      jobStatus,
      jobType,
      priority,
      openings,
      benefits,
      primarySkills,
      jobDescription,
      minSalary: salary.min,
      maxSalary: salary.max,
      currencySalary: salary.currency,
      jobProfile,
    })
      .filter(([key, value]) => {
        if (Array.isArray(value)) return value.length === 0;
        return value === undefined || value === null || value === "";
      })
      .map(([key]) => key);

    if (missingFields.length > 0) {
      return res.status(400).json({
        success: false,
        error: `Missing or invalid required field(s): ${missingFields.join(
          ", "
        )}`,
      });
    }

    if (jobType == "contract" && !payRate) {
      return res.status(400).json({
        success: false,
        error: `Contract pay rate is required.`,
      });
    }

    const updateJob = await Job.findOneAndUpdate(
      {
        jobId: jobID,
      },
      {
        $set: {
          jobTitle: jobTitle,
          industry: domain,
          externalJobId: externalJobID ? externalJobID : "",
          commission: {
            amount: commissionAmount,
            currency: commissionCurrency || "USD",
          },
          location: {
            country: jobCountry,
            city: jobCity || "",
            state: jobState || "",
            zipCode: JobZipCode || "",
          },
          experience: {
            min: experience.min,
            max: experience.max,
            unit: experience.unit || "years",
          },
          jobStatus: jobStatus || "open",
          jobType: jobType || "full-time",
          remote: remote || false,
          requiredHoursPerWeek: requiredHoursPerWeek || 40,
          priority: priority || "low priority",
          openings: openings || 1,
          salary: {
            min: salary.min,
            max: salary.max,
            currency: salary.currency || "USD",
          },
          payRate: jobType == "contract" ? payRate : null,
          clientname: clinetName || "",
          primarySkills: primarySkills
            ? primarySkills.filter((item) => item).map((skill) => skill.trim())
            : [],
          benefits: benefits
            ? benefits.filter((item) => item).map((benefit) => benefit.trim())
            : [],
          jobDescription: jobDescription || "",
          guaranteePeriod: guaranteePeriod || 60,
          jobProfile,
        },
        $push: {
          updateOn: {
            _id: new mongoose.Types.ObjectId(user._id),
            userID: user.userId,
          },
        },
      },
      { new: true }
    );

    if (!updateJob) {
      return res
        .send(400)
        .json({ success: false, message: "Job not found or failed to update" });
    }
    return res.status(200).json({ success: true, data: updateJob });
  } catch (error) {
    console.log(error);
    res.status(400).json({ success: false, message: req?.message });
  }
};

//* @Desc update job status
//* @Route POST /api/v1/job/updatejobstatus
//* @Access private, AccountManager
const updateJobStatus = async (req, res) => {
  try {
    const user = req?.user;

    if (!user) {
      res.status(400).json({ success: false, message: "user not found" });
    }
    const { jobID, status } = req.body;

    const jobStatus = [
      "Active",
      "Inactive",
      "Onhold",
      "Holdbyclient",
      "Filled",
      "Cancelled",
      "Closed",
    ];

    if (!jobStatus.includes(status)) {
      return res.status(400).json({
        success: false,
        message: "Job status does not match the predefined status.",
      });
    }

    let findQuery = {
      jobId: jobID,
      isDeleted: false,
    };

    if (user.userType == "accountManager") {
      findQuery = {
        isDeleted: false,
        jobId: jobID,
        "accountManager.userID": user.userId,
      };
    }

    const updateJob = await Job.findOneAndUpdate(
      findQuery,
      {
        $set: {
          jobStatus: status,
        },
        $push: {
          updateOn: {
            _id: user._id,
            userID: user.userId,
          },
        },
      },
      { new: true }
    );

    if (!updateJob) {
      return res
        .send(400)
        .json({ success: false, message: "Job not found or failed to update" });
    }
    return res.status(200).json({ success: true, data: updateJob });
  } catch (error) {
    console.log(error);
    res.status(400).json({ success: false, message: req?.message });
  }
};

//* @Desc get recruiter with jobs assign request to work
//* @Route GET /api/v1/job/getjobrecruiterworkonrequest/:jobID
//* @Access private, AccountManager, head acount Manager
const getJobRecruiterWorkOnRequest = async (req, res) => {
  try {
    const { jobID } = req.params;

    if (!jobID) {
      res.status(400).josn({ success: false, message: "job Id not found" });
    }

    const user = req?.user;

    if (!user) {
      res.status(400).json({ success: false, message: "user not found" });
    }

    let findQuery = {
      jobStatus: "Active",
      isDeleted: false,
      visibility: true,
      accountManager: { $exists: true },
      jobId: jobID,
    };

    if (user.userType == "accountManager") {
      findQuery = {
        jobStatus: "Active",
        isDeleted: false,
        visibility: true,
        accountManager: { $exists: true },
        jobId: jobID,
        "accountManager.userID": user.userId,
      };
    }

    const jobrecruiterworkonrequest = await job.aggregate([
      {
        $match: findQuery,
      },
      {
        $lookup: {
          from: "users",
          let: { managerId: "$accountManager._id" },

          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$_id", "$$managerId"],
                },
              },
            },
            {
              $project: {
                name: 1,
                userId: 1,
                email: 1,
                phone: 1,
              },
            },
          ],

          as: "accountManager",
        },
      },
      {
        $unwind: {
          path: "$accountManager",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          let: { jobID: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$job._id", "$$jobID"],
                },
              },
            },
            {
              $facet: {
                candidate: [
                  {
                    $group: {
                      _id: "$status",
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      status: "$_id",
                      count: "$count",
                      _id: false,
                    },
                  },
                ],
              },
            },
          ],
          as: "submission",
        },
      },
      {
        $unwind: {
          path: "$submission",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "recruiters",
          let: { jobID: "$_id" },
          pipeline: [
            {
              $unwind: {
                path: "$jobsWorkingOn",
                preserveNullAndEmptyArrays: false,
              },
            },

            {
              $match: {
                "jobsWorkingOn.status": "assigned",
                "jobsWorkingOn.isActive": true,
              },
            },
            {
              $group: {
                _id: "$jobsWorkingOn.jobId",
                count: {
                  $sum: 1,
                },
              },
            },

            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$_id", "$$jobID"],
                    },
                  ],
                },
              },
            },
          ],
          as: "recruiterCount",
        },
      },
      {
        $unwind: {
          path: "$recruiterCount",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "users",
          let: {
            userType: "recruiter",
            jobID: "$jobId",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$userType", "$$userType"],
                },
              },
            },
            {
              $lookup: {
                from: "recruiters",
                let: {
                  userId: "$userId",
                },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: ["$user.userId", "$$userId"],
                      },
                    },
                  },
                  {
                    $addFields: {
                      filteredJobs: {
                        $filter: {
                          input: "$jobsWorkingOn",
                          as: "job",
                          cond: {
                            $and: [
                              {
                                $eq: ["$$job.status", "assigned"],
                              },
                              {
                                $eq: ["$$job.isActive", true],
                              },
                            ],
                          },
                        },
                      },
                    },
                  },
                  {
                    $addFields: {
                      jobsWorkingOnCount: {
                        $cond: {
                          if: {
                            $isArray: "$filteredJobs",
                          },
                          then: {
                            $size: "$filteredJobs",
                          },
                          else: 0,
                        },
                      },
                    },
                  },
                  {
                    $project: {
                      _id: false,
                      jobsWorkingOnCount: 1,
                      domain: 1,
                      candidateRole: 1,
                    },
                  },
                ],
                as: "recruiterInfo",
              },
            },
            {
              $unwind: {
                path: "$recruiterInfo",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $lookup: {
                from: "candidatesubmissions",
                localField: "userId",
                foreignField: "submittedBy.userID",
                as: "candidatesubmissions",
              },
            },
            {
              $addFields: {
                candidatesubmissionCount: {
                  $cond: {
                    if: {
                      $isArray: "$candidatesubmissions",
                    },
                    then: {
                      $size: "$candidatesubmissions",
                    },
                    else: 0,
                  },
                },
              },
            },
            {
              $lookup: {
                from: "workrequests",
                let: {
                  userID: "$userId",
                  jobIDs: "$$jobID",
                },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          {
                            $eq: ["$job.jobId", "$$jobIDs"],
                          },
                          {
                            $eq: ["$recruiter.userId", "$$userID"],
                          },
                        ],
                      },
                    },
                  },
                ],
                as: "workinOnRequest",
              },
            },

            {
              $addFields: {
                assignStatus: {
                  $cond: {
                    if: {
                      $gt: [{ $size: "$workinOnRequest" }, 0],
                    },
                    then: {
                      $getField: {
                        field: "status",
                        input: {
                          $first: "$workinOnRequest",
                        },
                      },
                    },
                    else: null,
                  },
                },
              },
            },

            {
              $project: {
                userId: 1,
                name: 1,
                recruiterInfo: 1,
                candidatesubmissionCount: 1,
                assignStatus: 1,
              },
            },
          ],
          as: "recruiters",
        },
      },
    ]);

    return res.status(200).send({
      success: true,
      message: "get all job recruiter work on request",
      data: jobrecruiterworkonrequest,
    });
  } catch (err) {
    console.error("Error in job recruiter work on request:", err);
    res.status(400).json({
      success: false,
      message: "Something went wrong while fetching jobs",
      error: err.message,
    });
  }
};

//* @Desc get all recruiters working on a job
//* @Route GET /api/v1/job/get-all-recruiters-working-on-job
//* @Access private, AccountManager, head account Manager
const getAllRecruitersWorkingOnJob = async (req, res) => {
  try {
    const { jobId } = req.query;

    // Validate jobId if provided
    if (jobId && !mongoose.Types.ObjectId.isValid(jobId)) {
      return res.status(400).json({
        success: false,
        message: "Invalid jobId provided",
      });
    }

    const baseMatchCoinTxn = {
      transactionType: "spent",
      spendType: "workOn",
      status: "completed",
    };

    if (jobId) {
      baseMatchCoinTxn.relatedJobId = new mongoose.Types.ObjectId(jobId);
    }

    const baseMatchWorkRequest = {
      status: "accepted",
    };

    if (jobId) {
      baseMatchWorkRequest["job._id"] = new mongoose.Types.ObjectId(jobId);
    }

    const pipeline = [
      // From CoinTransactions
      { $match: baseMatchCoinTxn },
      {
        $project: {
          jobId: "$relatedJobId",
          recruiterId: "$userId",
        },
      },

      // Union WorkRequests
      {
        $unionWith: {
          coll: "workrequests",
          pipeline: [
            { $match: baseMatchWorkRequest },
            {
              $project: {
                jobId: "$job._id",
                recruiterId: "$recruiter._id",
              },
            },
          ],
        },
      },

      // Group recruiterIds by job
      {
        $group: {
          _id: "$jobId",
          recruiters: { $addToSet: "$recruiterId" },
        },
      },

      // Unwind recruiters to get user + profile
      { $unwind: "$recruiters" },

      // Lookup User Info
      {
        $lookup: {
          from: "users",
          localField: "recruiters",
          foreignField: "_id",
          as: "user",
        },
      },
      { $unwind: "$user" },

      // Lookup Recruiter Profile by user._id
      {
        $lookup: {
          from: "recruiters",
          let: { userId: "$user._id" },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ["$user._id", "$$userId"] },
              },
            },
          ],
          as: "profile",
        },
      },
      { $unwind: { path: "$profile", preserveNullAndEmptyArrays: true } },

      // Assemble recruiter with full profile
      {
        $addFields: {
          recruiter: {
            _id: "$user._id",
            name: "$user.name",
            email: "$user.email",
            profile: "$profile",
          },
        },
      },

      // Regroup recruiters under jobId
      {
        $group: {
          _id: "$_id", // jobId
          recruiters: { $push: "$recruiter" },
        },
      },
    ];

    const result = await CoinTransaction.aggregate(pipeline);

    if (!result || result.length === 0) {
      return res.status(200).json({
        success: true,
        message: "No recruiters found for the given criteria.",
        data: [],
      });
    }

    res.status(200).json({ success: true, data: result[0] });
  } catch (error) {
    console.error("Error fetching recruiters:", error);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
};

const getJobsByAccountManager = async (req, res) => {
  try {
    const userId = req.user?._id;

    const jobs = await Job.find({
      "accountManager._id": userId,
      visibility: true,
    }).select("_id jobId jobTitle jobType payRate guaranteePeriod");

    res.status(200).json({ success: true, data: jobs });
  } catch (error) {
    res.status(500).json({ error: "Internal Server Error" });
  }
};

module.exports = {
  getAllJobs,
  getSingleJob,
  createJob,
  updatejob,
  removeJob,
  bulkUpload,
  jobAssignToManager,
  publishJob,
  updateJob,
  getAllUnassignedJobs,
  getHighPeriorityJobs,
  getActiveJobs,
  getCloseJobs,
  getUnEngagedJobs,
  updateJobStatus,
  getJobRecruiterWorkOnRequest,
  getAllRecruitersWorkingOnJob,
  getJobsByAccountManager,
};
