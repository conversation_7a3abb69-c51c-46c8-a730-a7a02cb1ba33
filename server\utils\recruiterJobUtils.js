/**
 * Utility functions for recruiter job operations
 * Similar to jobQueryUtils.js but specific to recruiter job requirements
 */

/**
 * Build sort conditions for recruiter jobs
 * @param {string} sortBy - Field to sort by
 * @param {string} sortOrder - Sort order (asc/desc)
 * @param {string} postedDate - Date sorting preference (recent/oldest)
 * @returns {object} - MongoDB sort conditions
 */
const buildRecruiterSortConditions = (sortBy, sortOrder, postedDate) => {
  // Default to latest first
  const defaultSort = { createdAt: -1 };

  // If no parameters provided, return default sort
  if (!postedDate && !sortBy && !sortOrder) {
    return defaultSort;
  }

  // Handle sortBy first (highest priority)
  if (sortBy) {
    const sortField = sortBy.toLowerCase();
    // For date-based sorting, use explicit -1/1
    if (sortField === 'newest' || sortField === 'recent' || sortField === 'latest') {
      return { createdAt: -1 };
    }
    if (sortField === 'oldest') {
      return { createdAt: 1 };
    }
    // For other fields, use the provided sort order
    const order = sortOrder && sortOrder.toLowerCase() === 'asc' ? 1 : -1;

    // Direct date sorting
    if (sortField === 'newest' || sortField === 'recent' || sortField === 'latest') {
      return { createdAt: -1 };
    }
    if (sortField === 'oldest') {
      return { createdAt: 1 };
    }
  }

  // Fall back to postedDate if sortBy not specified
  if (postedDate) {
    const dateSort = postedDate.toLowerCase();
    if (['recent', 'newest', 'latest'].includes(dateSort)) {
      return { createdAt: -1 };
    } else if (['oldest', 'earliest'].includes(dateSort)) {
      return { createdAt: 1 };
    }
  }

  if (!sortBy) return defaultSort;

  const sortField = sortBy.toLowerCase();
  const order = sortOrder && sortOrder.toLowerCase() === 'asc' ? 1 : -1;

  const sortMappings = {
    'posteddate': { createdAt: order },
    'posted_date': { createdAt: order },
    'createdat': { createdAt: order },
    'created_at': { createdAt: order },
    'recent': { createdAt: -1 },
    'newest': { createdAt: -1 },
    'latest': { createdAt: -1 },
    'oldest': { createdAt: 1 },
    'jobtitle': { jobTitle: order },
    'job_title': { jobTitle: order },
    'jobtype': { jobType: order },
    'job_type': { jobType: order },
    'experience': { 'experience.min': order },
    'industry': { industry: order },
    'specialization': { industry: order },
    'priority': { priority: order },
    'updatedat': { updatedAt: order },
    'updated_at': { updatedAt: order },
    'location': { 'location.city': order }
  };

  return sortMappings[sortField] || defaultSort;
};

/**
 * Build filter conditions for recruiter jobs
 * @param {object} query - Request query parameters
 * @returns {object} - MongoDB filter conditions
 */
const buildRecruiterFilterConditions = (query) => {
  const filterConditions = {};
  
  // Specialization filter (maps to industry field)
  if (query.specialization) {
    const specializations = query.specialization.split(',').map(s => s.trim()).filter(s => s);
    if (specializations.length > 0) {
      filterConditions.industry = {
        $in: specializations.map(spec => new RegExp(spec.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i'))
      };
    }
  }

  // Location filter
  if (query.location) {
    const locations = query.location.split(',').map(l => l.trim()).filter(l => l);
    if (locations.length > 0) {
      filterConditions["location.country"] = {
        $in: locations.map(loc => new RegExp(loc.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i'))
      };
    }
  }

  // Country filter (separate from location for new filtering system)
  if (query.country) {
    const countries = query.country.split(',').map(c => c.trim()).filter(c => c);
    if (countries.length > 0) {
      filterConditions["location.country"] = {
        $in: countries.map(country => new RegExp(country.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i'))
      };
    }
  }

  // State filter
  if (query.state) {
    const states = query.state.split(',').map(s => s.trim()).filter(s => s);
    if (states.length > 0) {
      filterConditions["location.state"] = {
        $in: states.map(state => new RegExp(state.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i'))
      };
    }
  }

  // Job Type filter
  if (query.jobType) {
    const jobTypes = query.jobType.split(',').map(jt => jt.trim()).filter(jt => jt);
    if (jobTypes.length > 0) {
      filterConditions.jobType = {
        $in: jobTypes.map(type => new RegExp(`^${type.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`, 'i'))
      };
    }
  }

  // Experience Level filter
  if (query.experienceLevel) {
    const expLevels = query.experienceLevel.split(',').map(el => el.trim()).filter(el => el);
    if (expLevels.length > 0) {
      // Handle range format like "3-7"
      const expConditions = expLevels.map(range => {
        if (range.includes('-')) {
          const [min, max] = range.split('-').map(Number);
          return {
            $and: [
              { "experience.min": { $lte: max } },
              { "experience.max": { $gte: min } }
            ]
          };
        }
        return {};
      }).filter(cond => Object.keys(cond).length > 0);
      
      if (expConditions.length > 0) {
        filterConditions.$or = expConditions;
      }
    }
  }

  // Job Posted Date filter
  if (query.jobPosted) {
    const jobPostedFilters = query.jobPosted.split(',').map(jp => jp.trim()).filter(jp => jp);
    if (jobPostedFilters.length > 0) {
      const dateConditions = [];
      const now = new Date();

      jobPostedFilters.forEach(filter => {
        if (filter.startsWith('Custom:')) {
          // Handle custom date range: "Custom:2024-01-01 to 2024-01-31"
          const dateRange = filter.replace('Custom:', '');
          const [fromStr, toStr] = dateRange.split(' to ').map(d => d.trim());

          if (fromStr && toStr) {
            const fromDate = new Date(fromStr);
            const toDate = new Date(toStr);
            toDate.setHours(23, 59, 59, 999); // End of day

            dateConditions.push({
              createdAt: {
                $gte: fromDate,
                $lte: toDate
              }
            });
          }
        } else {
          // Handle predefined ranges
          let daysAgo;
          switch (filter) {
            case 'Past 7 days':
              daysAgo = 7;
              break;
            case 'Past 15 days':
              daysAgo = 15;
              break;
            case 'Past 30 days':
              daysAgo = 30;
              break;
            default:
              daysAgo = null;
          }

          if (daysAgo) {
            const pastDate = new Date(now);
            pastDate.setDate(now.getDate() - daysAgo);
            pastDate.setHours(0, 0, 0, 0); // Start of day

            dateConditions.push({
              createdAt: { $gte: pastDate }
            });
          }
        }
      });

      if (dateConditions.length > 0) {
        if (dateConditions.length === 1) {
          Object.assign(filterConditions, dateConditions[0]);
        } else {
          filterConditions.$or = filterConditions.$or ?
            [...filterConditions.$or, ...dateConditions] : dateConditions;
        }
      }
    }
  }

  // Number of Position filter
  if (query.numberOfPosition) {
    const positionRanges = query.numberOfPosition.split(',').map(np => np.trim()).filter(np => np);
    if (positionRanges.length > 0) {
      const positionConditions = [];

      positionRanges.forEach(range => {
        if (range.includes('-')) {
          const [min, max] = range.split('-').map(Number);
          if (!isNaN(min) && !isNaN(max)) {
            positionConditions.push({
              openings: {
                $gte: min,
                $lte: max
              }
            });
          }
        } else {
          // Handle single number or "+" ranges like "20+"
          const num = parseInt(range.replace('+', ''));
          if (!isNaN(num)) {
            if (range.includes('+')) {
              positionConditions.push({
                openings: { $gte: num }
              });
            } else {
              positionConditions.push({
                openings: num
              });
            }
          }
        }
      });

      if (positionConditions.length > 0) {
        if (positionConditions.length === 1) {
          Object.assign(filterConditions, positionConditions[0]);
        } else {
          filterConditions.$or = filterConditions.$or ?
            [...filterConditions.$or, ...positionConditions] : positionConditions;
        }
      }
    }
  }

  // Salary Range filter
  if (query.salaryRange) {
    const salaryRanges = query.salaryRange.split(',').map(sr => sr.trim()).filter(sr => sr);
    if (salaryRanges.length > 0) {
      const salaryConditions = [];

      salaryRanges.forEach(range => {
        if (range.includes('-')) {
          // Handle range format like "50000-100000" or "50k-100k"
          const [minStr, maxStr] = range.split('-').map(s => s.trim());

          // Convert k/K to thousands (e.g., "50k" -> 50000)
          const parseAmount = (str) => {
            if (str.toLowerCase().includes('k')) {
              return parseInt(str.replace(/k/i, '')) * 1000;
            }
            return parseInt(str);
          };

          const min = parseAmount(minStr);
          const max = parseAmount(maxStr);

          if (!isNaN(min) && !isNaN(max)) {
            salaryConditions.push({
              $and: [
                { "salary.min": { $lte: max } }, // Job's minimum salary is <= user's maximum
                { "salary.max": { $gte: min } }  // Job's maximum salary is >= user's minimum
              ]
            });
          }
        } else {
          // Handle single value or "+" ranges like "100k+"
          const parseAmount = (str) => {
            if (str.toLowerCase().includes('k')) {
              return parseInt(str.replace(/k/i, '').replace('+', '')) * 1000;
            }
            return parseInt(str.replace('+', ''));
          };

          const amount = parseAmount(range);
          if (!isNaN(amount)) {
            if (range.includes('+')) {
              // For "100k+", find jobs where max salary is >= 100k
              salaryConditions.push({
                "salary.max": { $gte: amount }
              });
            } else {
              // For exact amount, find jobs that include this amount in their range
              salaryConditions.push({
                $and: [
                  { "salary.min": { $lte: amount } },
                  { "salary.max": { $gte: amount } }
                ]
              });
            }
          }
        }
      });

      if (salaryConditions.length > 0) {
        if (salaryConditions.length === 1) {
          Object.assign(filterConditions, salaryConditions[0]);
        } else {
          filterConditions.$or = filterConditions.$or ?
            [...filterConditions.$or, ...salaryConditions] : salaryConditions;
        }
      }
    }
  }

  // Search functionality
  if (query.search && query.search.trim()) {
    const searchTerm = query.search.trim();
    const searchField = query.searchField || 'all';

    const searchConditions = [];

    if (searchField === 'all' || searchField === 'jobTitle') {
      searchConditions.push({ jobTitle: new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i') });
    }
    if (searchField === 'all' || searchField === 'jobId') {
      searchConditions.push({ jobId: new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i') });
    }
    if (searchField === 'all' || searchField === 'location') {
      searchConditions.push({ "location.country": new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i') });
    }
    if (searchField === 'all' || searchField === 'jobType') {
      searchConditions.push({ jobType: new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i') });
    }
    if (searchField === 'all' || searchField === 'industry') {
      searchConditions.push({ industry: new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i') });
    }
    if (searchField === 'all' || searchField === 'priority') {
      searchConditions.push({ priority: new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i') });
    }

    if (searchConditions.length > 0) {
      // If there's already an $or condition (from experience level), combine them properly
      if (filterConditions.$or) {
        // Combine existing $or with search $or using $and
        const existingOr = filterConditions.$or;
        delete filterConditions.$or;
        filterConditions.$and = [
          { $or: existingOr },
          { $or: searchConditions }
        ];
      } else {
        filterConditions.$or = searchConditions;
      }
    }
  }

  return filterConditions;
};

/**
 * Build response with applied filters and sorting
 * @param {object} query - Request query parameters
 * @param {object} sortConditions - Applied sort conditions
 * @returns {object} - Response metadata
 */
const buildRecruiterResponseMetadata = (query, sortConditions) => {
  return {
    appliedFilters: {
      specialization: query.specialization ? query.specialization.split(',').map(s => s.trim()).filter(s => s) : [],
      location: query.location ? query.location.split(',').map(l => l.trim()).filter(l => l) : [],
      country: query.country ? query.country.split(',').map(c => c.trim()).filter(c => c) : [],
      jobType: query.jobType ? query.jobType.split(',').map(jt => jt.trim()).filter(jt => jt) : [],
      experienceLevel: query.experienceLevel ? query.experienceLevel.split(',').map(el => el.trim()).filter(el => el) : [],
      search: query.search || null,
      searchField: query.searchField || null,
    },
    appliedSorting: {
      postedDate: query.postedDate || null,
      sortBy: query.sortBy || null,
      sortOrder: query.sortOrder || null,
      sortObject: sortConditions
    }
  };
};

module.exports = {
  buildRecruiterSortConditions,
  buildRecruiterFilterConditions,
  buildRecruiterResponseMetadata
};
