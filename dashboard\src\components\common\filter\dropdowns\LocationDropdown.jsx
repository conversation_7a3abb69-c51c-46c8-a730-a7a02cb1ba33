import React, { useState, useEffect, useRef } from "react";
import countries from "../../../../data/countries.json";

const LocationDropdown = ({
  label,
  selectedValues,
  filterKey,
  onLocalChange,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [localSelected, setLocalSelected] = useState(selectedValues || []);
  const [isOpen, setIsOpen] = useState(false);

  // Sync with parent when selectedValues change (for clear all functionality)
  React.useEffect(() => {
    setLocalSelected(selectedValues || []);
  }, [selectedValues]);

  // Get options based on filter type
  const getOptions = () => {
    if (filterKey === "state") {
      // Get all states from all countries
      const allStates = [];
      countries.forEach(country => {
        if (country.states && country.states.length > 0) {
          country.states.forEach(state => {
            allStates.push(state.name);
          });
        }
      });
      // Remove duplicates and sort
      return [...new Set(allStates)].sort();
    } else {
      // Default to countries
      return countries.map((country) => country.name);
    }
  };

  const options = getOptions();

  const toggleOption = (option) => {
    let updated;
    if (localSelected.includes(option)) {
      updated = localSelected.filter((item) => item !== option);
    } else {
      updated = [...localSelected, option];
    }
    setLocalSelected(updated);
    onLocalChange(filterKey, updated);
  };

  const removeTag = (tagToRemove) => {
    const updated = localSelected.filter((item) => item !== tagToRemove);
    setLocalSelected(updated);
    onLocalChange(filterKey, updated);
  };

  const filteredOptions = options.filter((opt) =>
    opt.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const closeDropdown = () => {
    setIsOpen(false);
    setSearchTerm("");
  };

  const wrapperRef = useRef(null);

  // Close when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        isOpen &&
        wrapperRef.current &&
        !wrapperRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () =>
      document.removeEventListener("mousedown", handleClickOutside);
  }, [isOpen]);

  return (
    <div className="relative" ref={wrapperRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-1 px-3 py-2 border border-[#E2E8F0] rounded-full bg-white text-[#475569] text-sm hover:bg-gray-50 cursor-pointer"
      >
        <span className="text-[#2E90FA] font-medium">
          {label}
          {localSelected.length > 0 ? `: ${localSelected.join(", ")}` : ""}
        </span>
        <svg
          className={`w-4 h-4 text-[#94A3B8] transition-transform ${
            isOpen ? "rotate-180" : ""
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 w-[350px] bg-white border border-gray-200 rounded-md shadow-lg z-50">
          {/* Header with close button */}
          <div className="flex items-center justify-between px-3 py-2 border-b border-gray-100">
            <p className="text-sm font-medium text-gray-700">
              {filterKey === "state" ? "State" : "Location"}
            </p>
            <button
              onClick={closeDropdown}
              className="text-gray-400 hover:text-gray-600 text-lg leading-none"
            >
              ×
            </button>
          </div>

          {/* Search Input */}
          <div className="p-3 border-b border-gray-100">
            <div className="relative">
              <input
                type="text"
                placeholder="Type to search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <svg
                className="absolute right-3 top-2.5 w-4 h-4 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>

          {/* Selected Tags */}
          {localSelected.length > 0 && (
            <div className="px-3 py-2 border-b border-gray-100">
              <div className="flex flex-wrap gap-1">
                {localSelected.map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center gap-1 bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full"
                  >
                    {tag}
                    <button
                      onClick={() => removeTag(tag)}
                      className="text-blue-500 hover:text-blue-700 ml-1"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Checkbox Grid */}
          <div className="max-h-60 overflow-y-auto">
            <div className="grid grid-cols-2 gap-x-2 gap-y-1 p-3">
              {filteredOptions.map((option) => (
                <label
                  key={option}
                  className="flex items-center gap-2 text-sm text-gray-700 cursor-pointer hover:bg-gray-50 px-1 py-0.5 rounded"
                >
                  <input
                    type="checkbox"
                    checked={localSelected.includes(option)}
                    onChange={() => toggleOption(option)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                  />
                  <span className="truncate">{option}</span>
                </label>
              ))}
            </div>
            {filteredOptions.length === 0 && (
              <div className="p-3 text-center text-gray-500 text-sm">
                {filterKey === "state" ? "No states found" : "No countries found"}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default LocationDropdown;
